package com.vgop.service.service;

import com.vgop.service.config.TaskConfig;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.config.VgopProperties;
import com.vgop.service.dto.TaskExecutionRequest;
import com.vgop.service.dto.TaskExecutionResponse;
import com.vgop.service.entity.TaskConfig.TaskStatus;
import com.vgop.service.exception.TaskExecutionException;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * VGOP任务调度服务
 */
@Slf4j
@Service("vgopTaskExecutor")
@RequiredArgsConstructor
public class VgopTaskScheduler {

    private final DataExportService dataExportService;
    private final FileProcessingService fileProcessingService;
    private final RevisionService revisionService;
    private final VgopAppConfig vgopAppConfig;
    private final VgopProperties vgopProperties;
    private final DataValidationService dataValidationService;

    /**
     * 根据接口ID执行单个统计任务
     */
    @Async("taskExecutor")
    public CompletableFuture<TaskExecutionResponse> executeTask(TaskExecutionRequest request) {
        String interfaceId = request.getInterfaceId();
        log.info("开始执行统计任务: {}, 接口ID: {}", request.getActionInstanceId(), interfaceId);

        LocalDateTime startTime = LocalDateTime.now();
        TaskExecutionResponse.TaskExecutionResponseBuilder responseBuilder = TaskExecutionResponse.builder()
                .taskId(request.getActionInstanceId())
                .status(TaskStatus.RUNNING)
                .startTime(startTime)
                .success(false);

        try {
            // 1. 根据interfaceId查找任务配置
            TaskConfig taskConfig = vgopAppConfig.getDailyTask(interfaceId);
            if (taskConfig == null) {
                taskConfig = vgopAppConfig.getMonthlyTask(interfaceId);
            }
            if (taskConfig == null) {
                throw new TaskExecutionException("未找到接口ID为 '" + interfaceId + "' 的任务配置");
            }

            // 2. 执行数据导出
            DataExportService.ExportResult exportResult = dataExportService.exportData(taskConfig, request.getDateId(), 1);
            if (!exportResult.isSuccess()) {
                throw new TaskExecutionException("数据导出失败: " + exportResult.getErrorMessage());
            }

            // 3. 定位导出的unl文件
            String exportRoot = vgopProperties.getExportPath();
            String cycleType = taskConfig.getTaskType().equals("daily") ? "day" : "month";
            String exportPath = String.format("%s/%s/%s/", exportRoot, request.getDateId(), cycleType);
            
            String tempFileName = taskConfig.getExport().getTempFileNameTemplate()
                    .replace("{dataDate}", request.getDateId())
                    .replace("{interfaceId}", interfaceId);
            
             if (taskConfig.getTaskType().equals("daily")) {
                String beforeDay = DateTimeUtil.calculateBeforeDay(request.getDateId());
                tempFileName = tempFileName.replace("{previousDay}", beforeDay);
            }

            String unloadFilePath = exportPath + tempFileName;
            
            if (!new File(unloadFilePath).exists()) {
                 throw new TaskExecutionException("导出的临时文件不存在: " + unloadFilePath);
            }

            // 4. 对导出的unl文件进行校验和清理
            String cleanedFilePath = dataValidationService.validateAndCleanFile(unloadFilePath, interfaceId, request.getDateId(), taskConfig.getTaskType());

            // 5. 处理生成的文件（分割、重命名等），使用清理后的文件
            String outputDir = processFile(cleanedFilePath, request, taskConfig);
            
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();

            responseBuilder.status(TaskStatus.SUCCESS)
                    .endTime(endTime)
                    .executionTimeMs(executionTime)
                    .generatedFiles(outputDir != null ? Collections.singletonList(outputDir) : Collections.emptyList())
                    .success(true)
                    .message("任务 " + interfaceId + " 执行成功");

            log.info("统计任务执行完成: {}, 接口ID: {}", request.getActionInstanceId(), interfaceId);

        } catch (Exception e) {
            LocalDateTime endTime = LocalDateTime.now();
            long executionTime = java.time.Duration.between(startTime, endTime).toMillis();

            responseBuilder.status(TaskStatus.FAILED)
                    .endTime(endTime)
                    .executionTimeMs(executionTime)
                    .errorMessage(e.getMessage())
                    .message("任务 " + interfaceId + " 执行失败");

            log.error("统计任务 {} 执行失败, 接口ID: {}", request.getActionInstanceId(), interfaceId, e);
        }

        return CompletableFuture.completedFuture(responseBuilder.build());
    }

    /**
     * 处理文件（通用版本）
     */
    private String processFile(String unloadFilePath, TaskExecutionRequest request, TaskConfig taskConfig) {
        if (unloadFilePath == null) {
            return null;
        }

        String dataDate = request.getDateId();
        String cycleType = taskConfig.getTaskType();
        String outputDir = fileProcessingService.generateOutputDirectory(
                request.getImagePath(), dataDate, cycleType);
        fileProcessingService.ensureDirectoryExists(outputDir);
        
        // 使用文件名模板生成不带后缀的文件名
        String baseFileName = taskConfig.getExport().getOutputFileNameTemplate()
            .replace("{dataDate}", dataDate)
            .replace("_{revTimes}", "")
            .replace("_{fileNum}", "")
            .replace(".dat", "");

        String revision = revisionService.getNextRevision(dataDate, baseFileName);

        // 文件处理服务需要知道文件名前缀和脚本ID来进行分割和命名
        // 我们从文件名模板中提取这些信息
        String[] nameParts = baseFileName.split("_");
        String filePrefix = nameParts[0] + "_" + nameParts[1]; // e.g., "a_10000" or "i_10000"
        String scriptIdentifier = taskConfig.getInterfaceName(); // e.g., "VGOP1-R2.10-24201"

        fileProcessingService.splitAndProcessFile(unloadFilePath, outputDir,
                filePrefix, scriptIdentifier, dataDate, revision);

        return outputDir;
    }
} 