2025-06-20 15:24:14.014 [vgop-task-1] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:24:14.028 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:24:14.029 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:52:13.865 [vgop-task-2] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:52:13.870 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:52:13.874 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:53:27.845 [vgop-task-1] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:53:27.850 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:53:27.851 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:53:27.866 [vgop-task-1] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=0
2025-06-20 15:54:43.300 [vgop-task-2] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:54:43.300 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:54:43.301 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:54:43.317 [vgop-task-2] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=0
2025-06-20 15:55:00.313 [vgop-task-3] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:55:00.314 [vgop-task-3] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:55:00.314 [vgop-task-3] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:55:00.322 [vgop-task-3] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 15:55:25.043 [vgop-task-4] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:55:25.044 [vgop-task-4] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:55:25.044 [vgop-task-4] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:55:25.050 [vgop-task-4] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 15:56:06.328 [vgop-task-5] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:56:06.329 [vgop-task-5] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:56:06.329 [vgop-task-5] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:56:06.335 [vgop-task-5] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 15:57:17.562 [vgop-task-1] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 15:57:17.562 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 15:57:17.563 [vgop-task-1] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 15:57:17.568 [vgop-task-1] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 16:00:32.967 [vgop-task-2] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 16:00:32.967 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 16:00:32.968 [vgop-task-2] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 16:00:32.982 [vgop-task-2] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 16:00:53.341 [vgop-task-3] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 16:00:53.342 [vgop-task-3] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 16:00:53.342 [vgop-task-3] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 16:00:53.353 [vgop-task-3] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
2025-06-20 16:02:26.898 [vgop-task-4] INFO  c.v.s.service.DataExportService - 开始导出接口数据: interfaceId=24201, dataDate=20250620, revision=1
2025-06-20 16:02:26.899 [vgop-task-4] DEBUG c.v.s.service.DataExportService - 构建UNLOAD SQL: SELECT mum.phonenumber AS phonenumber, mum.phonestate AS phonestate, mum.phoneimsi AS phoneimsi, mum.phoneimei AS phoneimei, mum.locationid AS locationid, SUBSTR(bp.bossid, 1, 3) AS provinceid, mum.openingtime AS openingtime, mum.Optime AS Optime, mum.sex AS sex FROM mcn_user_major mum LEFT JOIN bossprovince bp ON mum.provinceid = bp.provinceid WHERE mum.openingtime >= '20250619000000' AND mum.openingtime < '20250620235959' AND mum.phonestate IN ('0','1')
2025-06-20 16:02:26.899 [vgop-task-4] DEBUG c.v.s.service.DataExportService - 临时文件路径: ./VGOPdata/datafile/20250620/day/a_10000_20250620_VGOP1-R2.10-24201.unl
2025-06-20 16:02:26.904 [vgop-task-4] INFO  c.v.s.service.DataExportService - 接口数据导出成功: interfaceId=24201, 记录数=1000
