package com.vgop.service.service;

import com.vgop.service.config.ValidationRulesProperties;
import com.vgop.service.dao.ValidationAlertsMapper;
import com.vgop.service.entity.ValidationAlert;
import com.vgop.service.validation.ValidationEngine;
import com.vgop.service.validation.ValidationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataValidationServiceImpl implements DataValidationService {

    private final ValidationEngine validationEngine;
    private final ValidationRulesProperties validationRulesProperties;
    private final ValidationAlertsMapper validationAlertsMapper;

    @Override
    public String validateAndCleanFile(String originalFilePath, String interfaceId, String dataDate, String taskType) throws IOException {
        log.info("开始校验和清理文件: {}, 接口ID: {}", originalFilePath, interfaceId);
        File originalFile = new File(originalFilePath);
        if (!originalFile.exists()) {
            throw new IOException("原始文件不存在: " + originalFilePath);
        }

        String cleanedFilePath = originalFilePath.replace(".unl", ".cleaned.unl");
        Path tempPath = null;
        long lineNumber = 1;

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(originalFilePath), StandardCharsets.UTF_8)) {
            tempPath = Files.createTempFile("validation_", ".tmp");
            try (BufferedWriter writer = Files.newBufferedWriter(tempPath, StandardCharsets.UTF_8)) {
                String line;
                while ((line = reader.readLine()) != null) {
                    ValidationResult result = validationEngine.validateLine(line, interfaceId);
                    if (result.isValid()) {
                        writer.write(line);
                        writer.newLine();
                    } else {
                        log.warn("文件 {} 第 {} 行校验失败: {}", originalFile.getName(), lineNumber, result.getErrorMessages());
                        saveValidationAlert(result, interfaceId, originalFile.getName(), lineNumber, line);
                    }
                    lineNumber++;
                }
            }
        }

        Files.move(tempPath, Paths.get(cleanedFilePath), StandardCopyOption.REPLACE_EXISTING);
        log.info("文件校验和清理完成, 清理后的文件: {}", cleanedFilePath);

        return cleanedFilePath;
    }

    private void saveValidationAlert(ValidationResult result, String interfaceId, String fileName, long lineNumber, String errorData) {
        try {
            ValidationRulesProperties.InterfaceConfig interfaceConfig = validationRulesProperties.getInterfaces().get(interfaceId);
            String interfaceName = (interfaceConfig != null && interfaceConfig.getName() != null) ? interfaceConfig.getName() : interfaceId;

            ValidationAlert alert = ValidationAlert.builder()
                .alertTime(LocalDateTime.now())
                .interfaceName(interfaceName)
                .alertType("数据校验失败")
                .alertLevel(ValidationAlert.AlertLevel.ERROR)
                .alertMessage(String.join("; ", result.getErrorMessages()))
                .fileName(fileName)
                .lineNumber(lineNumber)
                .errorData(errorData)
                .status(ValidationAlert.AlertStatus.NEW)
                .build();
            validationAlertsMapper.insert(alert);
        } catch (Exception e) {
            log.error("保存校验告警信息失败", e);
        }
    }
} 