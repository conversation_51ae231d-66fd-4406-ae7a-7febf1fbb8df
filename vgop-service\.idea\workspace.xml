<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="72bd0a04-d42d-40a5-9237-44fc7aac0b16" name="Changes" comment="feat: 数据源调整">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/VgopTaskScheduler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/VgopTaskScheduler.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="C:\Users\<USER>\.m2\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yLqfOgQMKiFmeCQXpf42qjKnFq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.FileValidator.executor": "Debug",
    "Application.VgopServiceApplication.executor": "Debug",
    "Maven.vgop-service [clean].executor": "Run",
    "Maven.vgop-service [install].executor": "Run",
    "Maven.vgop-service [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.VgopServiceApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/workspaces/zjh/vgop/vgop-service/VGOPdata/datafile/20250620/day",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDKs",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.editor",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250620\day" />
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250618\day" />
      <recent name="C:\workspaces\zjh\vgop\vgop-service\VGOPdata\datafile\20250617\day" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.VgopServiceApplication">
    <configuration name="FileValidator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.vgop.service.validation.FileValidator" />
      <module name="vgop-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.vgop.service.validation.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="VgopServiceApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.vgop.service.VgopServiceApplication" />
      <module name="vgop-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.vgop.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="VgopServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="vgop-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.vgop.service.VgopServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.FileValidator" />
        <item itemvalue="Application.VgopServiceApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="72bd0a04-d42d-40a5-9237-44fc7aac0b16" name="Changes" comment="" />
      <created>1749624303978</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749624303978</updated>
      <workItem from="1749624305173" duration="3881000" />
      <workItem from="1749709425498" duration="1486000" />
      <workItem from="1749710925321" duration="11311000" />
      <workItem from="1749780857640" duration="13293000" />
      <workItem from="1750041151551" duration="1191000" />
      <workItem from="1750127692282" duration="2222000" />
      <workItem from="1750153051440" duration="1517000" />
      <workItem from="1750212280804" duration="15477000" />
      <workItem from="1750297867367" duration="7751000" />
      <workItem from="1750316296818" duration="5115000" />
      <workItem from="1750400028659" duration="5313000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 数据源调整">
      <option name="closed" value="true" />
      <created>1749720421771</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749720421771</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 数据源调整" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 数据源调整" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/vgop/service/service/FileTransferService.java</url>
          <line>172</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>